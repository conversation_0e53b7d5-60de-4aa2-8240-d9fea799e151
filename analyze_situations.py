import pandas as pd

def analyze_situation_codes():
    """Analyze all situationCode values to understand game situations"""
    
    # Load the data
    pbp_df = pd.read_csv('game_2023020001_with_players.csv')
    
    # Get unique situation codes and their counts
    situation_counts = pbp_df['situationCode'].value_counts().sort_index()
    
    print("🏒 SITUATION CODE ANALYSIS")
    print("=" * 50)
    print("SituationCode | Count | Description")
    print("-" * 50)
    
    for code, count in situation_counts.items():
        if pd.notna(code):
            # Parse the situation code
            code_str = str(int(code))
            if len(code_str) == 4:
                home_skaters = int(code_str[0])
                home_goalies = int(code_str[1])
                away_skaters = int(code_str[2])
                away_goalies = int(code_str[3])
                
                description = f"{home_skaters+home_goalies}v{away_skaters+away_goalies}"
                if home_goalies == 0:
                    description += " (Home Empty Net)"
                elif away_goalies == 0:
                    description += " (Away Empty Net)"
                elif home_skaters == 5 and away_skaters == 5:
                    description += " (Even Strength)"
                elif home_skaters > away_skaters:
                    description += " (Home Power Play)"
                elif away_skaters > home_skaters:
                    description += " (Away Power Play)"
                
                print(f"{code:>12} | {count:>5} | {description}")
    
    # Now let's look at shot attempts specifically
    shot_attempts = pbp_df[pbp_df['typeCode'].isin([505, 506, 507, 508])]
    
    print("\n🎯 SHOT ATTEMPTS BY SITUATION")
    print("=" * 50)
    shot_situations = shot_attempts['situationCode'].value_counts().sort_index()
    
    for code, count in shot_situations.items():
        if pd.notna(code):
            code_str = str(int(code))
            if len(code_str) == 4:
                home_skaters = int(code_str[0])
                home_goalies = int(code_str[1])
                away_skaters = int(code_str[2])
                away_goalies = int(code_str[3])
                
                description = f"{home_skaters+home_goalies}v{away_skaters+away_goalies}"
                if home_goalies == 0:
                    description += " (Home Empty Net)"
                elif away_goalies == 0:
                    description += " (Away Empty Net)"
                elif home_skaters == 5 and away_skaters == 5:
                    description += " (5v5 Even Strength)"
                
                print(f"{code:>12} | {count:>5} | {description}")

if __name__ == "__main__":
    analyze_situation_codes()
